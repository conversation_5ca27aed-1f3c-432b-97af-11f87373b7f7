"""
Enhanced <PERSON><PERSON><PERSON><PERSON> agent for extracting structured data from expedition documents.
Replaces Docling with simple text extraction and improved LLM prompting.
"""

import json
import logging
from datetime import datetime
from typing import Any, Dict, List, Optional

from langchain.agents import AgentExecutor, create_react_agent
from langchain.prompts import PromptTemplate
from langchain.tools import StructuredTool
from langchain_ollama import OllamaLLM

from ..config.langchain_config import AGENT_CONFIG, get_ollama_model_config
from ..core.text_extractor import create_text_extractor
from ..data.template_models import ExpeditionTemplate
from ..tools.text_extractor_tool import (
    create_multi_document_extractor_tool,
    create_text_extractor_tool,
)
from ..utils.json_validator import validate_and_repair_json

logger = logging.getLogger(__name__)


class DataExtractionAgent:
    """Enhanced agent for extracting structured data from expedition documents."""

    def __init__(self):
        """Initialize the data extraction agent."""
        self.llm = self._initialize_llm()
        self.text_extractor = create_text_extractor()
        self.tools = self._initialize_tools()
        self.agent_executor = self._create_agent()

    def _initialize_llm(self) -> OllamaLLM:
        """Initialize the Ollama LLM with optimized settings."""
        config = get_ollama_model_config()
        return OllamaLLM(
            model=config["model"],
            base_url=config["base_url"],
            temperature=0.2,  # Lower temperature for consistency
            num_ctx=config["num_ctx"],
        )

    def _initialize_tools(self) -> List:
        """Initialize tools for the agent."""
        return [
            create_text_extractor_tool(),
            create_multi_document_extractor_tool(),
            self._create_data_mapping_tool(),
            self._create_pattern_recognition_tool(),
        ]

    def _create_agent(self) -> AgentExecutor:
        """Create the data extraction agent with improved ReAct format handling."""
        prompt = PromptTemplate.from_template("""
You are an expert expedition data extraction specialist. Your task is to extract structured data from expedition documents and format it according to the expedition template specification.

CRITICAL: You MUST follow the exact format below. Do not deviate from this format or add extra text.

You have access to the following tools:
{tools}

Use the following format EXACTLY:

Question: the input question you must answer
Thought: you should always think about what to do
Action: the action to take, should be one of [{tool_names}]
Action Input: the input to the action
Observation: the result of the action
... (this Thought/Action/Action Input/Observation can repeat N times)
Thought: I now know the final answer
Final Answer: the final answer to the original input question

IMPORTANT FORMATTING RULES:
1. Always start with "Thought:" followed by your reasoning
2. Always follow with "Action:" and specify exactly one tool name from the list
3. Always follow with "Action Input:" and provide the required input
4. Wait for the "Observation:" before continuing
5. When you have enough information, start with "Thought: I now know the final answer"
6. End with "Final Answer:" containing the complete JSON result

Your extraction workflow:
1. First use "text_extractor" or "multi_document_extractor" to get raw text
2. Then use "data_mapper" to extract ALL structured data from the text (this should be comprehensive)
3. Only use "pattern_recognizer" if you need additional analysis insights (not for core data extraction)
4. Return the final structured JSON in the Final Answer

IMPORTANT: The data_mapper tool should extract ALL core expedition data including:
- Complete schedule with all time-based events
- All equipment details and counts
- All personnel information and roles
- All group assignments and activities
- Weather conditions and environmental factors
- Tide information if present
- Operational notes and observations

EXAMPLE FORMAT:
Question: Extract data from expedition document
Thought: I need to extract text from the document first
Action: text_extractor
Action Input: /path/to/document.pdf
Observation: [extracted text content]
Thought: Now I need to map this text to structured data
Action: data_mapper
Action Input: [extracted text content]
Observation: [structured JSON data]
Thought: I now know the final answer
Final Answer: [complete JSON structure]

Question: {input}
{agent_scratchpad}
""")

        agent = create_react_agent(self.llm, self.tools, prompt)

        return AgentExecutor(
            agent=agent,
            tools=self.tools,
            verbose=AGENT_CONFIG["verbose"],
            max_iterations=AGENT_CONFIG["max_iterations"],
            max_execution_time=AGENT_CONFIG["max_execution_time"],
            return_intermediate_steps=AGENT_CONFIG["return_intermediate_steps"],
            early_stopping_method=AGENT_CONFIG.get("early_stopping_method", "force"),
            handle_parsing_errors=True,  # Always enable parsing error handling
            trim_intermediate_steps=AGENT_CONFIG.get("trim_intermediate_steps", 10),
        )

    def _create_text_extraction_tool(self) -> StructuredTool:
        """Create text extraction tool."""

        def extract_text_from_file(file_path: str) -> str:
            """Extract raw text from expedition document files."""
            try:
                text = self.text_extractor.extract_text(file_path)
                if text:
                    return f"Successfully extracted text from {file_path}:\n\n{text}"
                else:
                    return f"Failed to extract text from {file_path}"
            except Exception as e:
                return f"Error extracting text from {file_path}: {e!s}"

        return StructuredTool.from_function(
            func=extract_text_from_file,
            name="text_extractor",
            description="Extract raw text content from expedition documents (PDF, DOCX, images, etc.)",
        )

    def _create_data_mapping_tool(self) -> StructuredTool:
        """Create data mapping tool with contextual inference and structured validation."""

        def map_expedition_data(raw_text: str, location: str = "") -> str:
            """Map raw text to expedition template structure with focused extraction and validation."""
            try:
                # Validate input
                if not raw_text or len(raw_text.strip()) < 10:
                    return json.dumps({
                        "error": "Insufficient text content for extraction",
                        "location": location or "Not specified"
                    })

                # First, extract structured data using regex patterns
                structured_data = self._extract_structured_data(raw_text, location)
                
                # Then use LLM to enhance the structured data
                enhanced_data = self._enhance_with_llm(raw_text, structured_data)
                
                # Validate and clean the enhanced data
                validated_data = self._validate_extracted_data(enhanced_data)
                
                # Return the validated data
                return json.dumps(validated_data, indent=2)

            except Exception as e:
                logger.error(f"Error in data mapping tool: {e}")
                return self._create_basic_extraction_from_text(raw_text, location)
                
        return StructuredTool.from_function(
            func=map_expedition_data,
            name="data_mapper",
            description="Map raw text to structured expedition template with focused data extraction",
        )
        
    def _extract_structured_data(self, raw_text: str, location: str = "") -> dict:
        """Extract structured data using regex patterns first, with improved group, movie, and notes extraction."""
        import re

        # Initialize structured data with defaults
        structured_data = {
            "date": None,
            "location": location or None,
            "arrival_time": None,
            "departure_time": None,
            "operation_type": "combined",
            "groups": [],
            "schedule": [],
            "tides": [],
            "equipment": {"zodiacs": 0, "twins": 0, "other": []},
            "personnel": {"total_count": 0, "guides": [], "drivers": []},
            "notes": "",
            "weather": "Not specified"
        }

        text = raw_text

        # Extract date patterns (various formats)
        date_patterns = [
            r'\b(\d{1,2}(?:st|nd|rd|th)?\s+(?:Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)[a-z]*\s+\d{4})\b',  # 22nd July 2024
            r'\b(\d{4}-\d{2}-\d{2})\b',  # 2024-07-22
            r'\b(\d{1,2}/\d{1,2}/\d{4})\b',  # 7/22/2024 or 22/7/2024
            r'\b((?:Monday|Tuesday|Wednesday|Thursday|Friday|Saturday|Sunday),?\s+\d{1,2}(?:st|nd|rd|th)?\s+(?:Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)[a-z]*)\b'  # Tuesday, 22nd July
        ]
        for pattern in date_patterns:
            date_match = re.search(pattern, text, re.IGNORECASE)
            if date_match:
                structured_data["date"] = date_match.group(1)
                break

        # Extract location if not provided
        if not structured_data["location"]:
            location_patterns = [
                r'(?:at|in|location[:\s]+)([A-Z][a-z]+ [A-Z][a-z]+(?: [A-Z][a-z]+)?)',  # Location: King George River
                r'(?:site|destination)[:\s]+([A-Z][a-z]+ [A-Z][a-z]+(?: [A-Z][a-z]+)?)'  # Site: Montgomery Reef
            ]
            for pattern in location_patterns:
                location_match = re.search(pattern, text)
                if location_match:
                    structured_data["location"] = location_match.group(1)
                    break

        # Extract all times
        time_matches = re.findall(r'\b(\d{1,2}:\d{2})\b', text)
        if time_matches:
            # First time is likely arrival
            structured_data["arrival_time"] = time_matches[0]
            # Last time is likely departure
            structured_data["departure_time"] = time_matches[-1]

            # Create schedule entries for each time
            for i, time in enumerate(time_matches):
                description = ""
                # Look for context around this time
                time_context_match = re.search(r'([^.!?]*\b' + time + r'\b[^.!?]*[.!?])', text)
                if time_context_match:
                    description = time_context_match.group(1).strip()
                structured_data["schedule"].append({
                    "time": time,
                    "type": "activity" if i > 0 and i < len(time_matches) - 1 else ("arrival" if i == 0 else "departure"),
                    "description": description or f"Scheduled event at {time}",
                    "location": structured_data["location"] or "Not specified"
                })

        # --- Improved Group Disembarkation and Return Events Extraction ---
        # Extract group disembarkation and return events
        group_pattern = re.compile(
            r'(?P<group_name>Yellow|Blue|Red|Green) Group (Disembarkation|returns|Returns).*?(?P<details>Zodiac tour #[0-9]+|backload at marina|end of operations|tour #[0-9]+|)', 
            re.IGNORECASE
        )
        structured_data['groups'] = []
        for match in group_pattern.finditer(text):
            group_info = {
                "group": match.group("group_name"),
                "event": match.group(2).capitalize(),
                "details": match.group("details").strip()
            }
            structured_data['groups'].append(group_info)

        # --- Movie Listings Extraction ---
        # Extract movie listings
        movie_pattern = re.compile(r'Movie: (?P<title>.+?) – (?P<location>Main Theatre|[A-Za-z ]+)', re.IGNORECASE)
        structured_data['movies'] = []
        for match in movie_pattern.finditer(text):
            structured_data['movies'].append({
                "title": match.group("title").strip(),
                "location": match.group("location").strip()
            })

        # --- Equipment Extraction ---
        zodiac_match = re.search(r'(\d+)\s*zodiac', text, re.IGNORECASE)
        if zodiac_match:
            structured_data["equipment"]["zodiacs"] = int(zodiac_match.group(1))
        twin_match = re.search(r'(\d+)\s*twin', text, re.IGNORECASE)
        if twin_match:
            structured_data["equipment"]["twins"] = int(twin_match.group(1))

        # --- Tide Extraction ---
        tide_patterns = [
            r'(?:high|low)\s+tide\s+(?:at|:)?\s+(\d{1,2}:\d{2})',  # High tide at 15:30
            r'(\d{1,2}:\d{2})\s+(?:high|low)\s+tide'  # 15:30 high tide
        ]
        for pattern in tide_patterns:
            tide_matches = re.findall(pattern, text, re.IGNORECASE)
            for tide_time in tide_matches:
                tide_type = "High Tide" if "high" in text[max(0, text.find(tide_time)-20):min(len(text), text.find(tide_time)+20)].lower() else "Low Tide"
                structured_data["tides"].append({
                    "time": tide_time,
                    "height": 12.0 if tide_type == "High Tide" else 2.0,  # Default values
                    "label": tide_type
                })

        # --- Weather Extraction ---
        weather_patterns = [
            r'weather[:\s]+([^.!?]+[.!?])',
            r'conditions[:\s]+([^.!?]+[.!?])'
        ]
        for pattern in weather_patterns:
            weather_match = re.search(pattern, text, re.IGNORECASE)
            if weather_match:
                structured_data["weather"] = weather_match.group(1).strip()
                break

        # --- Operation Type Extraction ---
        if structured_data["arrival_time"] and structured_data["departure_time"]:
            try:
                arrival_hour = int(structured_data["arrival_time"].split(":")[0])
                departure_hour = int(structured_data["departure_time"].split(":")[0])
                if arrival_hour < 12 and departure_hour < 12:
                    structured_data["operation_type"] = "am_only"
                elif arrival_hour >= 12 and departure_hour >= 12:
                    structured_data["operation_type"] = "pm_only"
                else:
                    structured_data["operation_type"] = "combined"
            except Exception:
                pass

        # --- Notes Extraction (special instructions and bar boat) ---
        notes_section = []
        if "Notes:" in text:
            notes_match = re.search(r'Notes:(.+?)(\n[A-Z][a-z]|$)', text, re.DOTALL)
            if notes_match:
                notes_section.append(notes_match.group(1))
        if "Bar Boat" in text:
            notes_section.append("Bar Boat included (keep secret from guests).")
        if "restaurant to open early" in text:
            notes_section.append("Request: Open restaurant early for zodiac tour departure.")
        structured_data['notes'] = [n.strip() for n in notes_section if isinstance(n, str) or n]

        return structured_data
        
    def _enhance_with_llm(self, raw_text: str, structured_data: dict) -> dict:
        """Use LLM to enhance the structured data extracted by regex."""
        try:
            # Create a prompt that shows the LLM what we've already extracted
            # and asks it to fill in missing information or correct errors
            enhanced_prompt = f"""Extract comprehensive expedition data from this document text and enhance the existing data.

DOCUMENT TEXT:
{raw_text}

CURRENT EXTRACTED DATA:
{json.dumps(structured_data, indent=2)}

Instructions:
1. KEEP all data that has already been extracted
2. ADD any missing information from the document text
3. CORRECT any errors in the extracted data
4. Extract ALL time-based events for the schedule array
5. Extract ALL group information with colors, times, and activities
6. Extract equipment counts (zodiacs, twins)
7. Extract personnel information (guides, drivers)
8. Extract tide data with times and heights
9. Extract detailed operational notes
10. Return ONLY valid JSON with no additional text

REQUIRED JSON STRUCTURE:
{{
  "date": "YYYY-MM-DD",
  "location": "Location Name",
  "arrival_time": "HH:MM",
  "departure_time": "HH:MM",
  "operation_type": "am_only|pm_only|combined",
  "groups": [
    {{
      "groupName": "Color",
      "color": "Color",
      "departureTime": "HH:MM",
      "returnTime": "HH:MM",
      "activity": "Activity Description"
    }}
  ],
  "schedule": [
    {{
      "time": "HH:MM",
      "type": "arrival|departure|briefing|disembarkation|zodiac_drop|custom",
      "description": "Event description",
      "group": "Group Color (if applicable)"
    }}
  ],
  "tides": [
    {{
      "time": "HH:MM",
      "height": 2.0,
      "label": "Low Tide|High Tide"
    }}
  ],
  "equipment": {{
    "zodiacs": 8,
    "twins": 1,
    "other": []
  }},
  "personnel": {{
    "total_count": 0,
    "guides": [],
    "drivers": []
  }},
  "weather": "Weather description",
  "notes": "Operational notes and observations"
}}

Return ONLY the enhanced JSON structure with no additional text."""

            # Get response from LLM
            response = self.llm.invoke(enhanced_prompt)
            
            # Clean up response and extract JSON
            response = response.strip()
            
            # Remove common prefixes
            prefixes_to_remove = [
                "ENHANCED EXTRACTED DATA:",
                "ENHANCED DATA:",
                "RESULT:",
                "JSON:",
                "```json",
                "```"
            ]
            
            for prefix in prefixes_to_remove:
                if response.startswith(prefix):
                    response = response.replace(prefix, "").strip()
                    
            # Try to extract JSON from response
            import re
            json_match = re.search(r'\{.*\}', response, re.DOTALL)
            if json_match:
                json_str = json_match.group(0)
                try:
                    # Parse the JSON
                    enhanced_data = json.loads(json_str)
                    logger.info("Successfully enhanced data with LLM")
                    
                    # Merge with original structured data to ensure we don't lose information
                    # but prioritize LLM enhancements
                    for key, value in enhanced_data.items():
                        if value is not None and value != "" and value != [] and value != {}:
                            structured_data[key] = value
                            
                    return structured_data
                except json.JSONDecodeError as e:
                    logger.warning(f"JSON parsing failed in LLM enhancement: {e}")
                    return structured_data
            else:
                logger.warning("No valid JSON found in LLM enhancement response")
                return structured_data
                
        except Exception as e:
            logger.error(f"Error in LLM enhancement: {e}")
            return structured_data
            
    def _validate_extracted_data(self, data: dict) -> dict:
        """Validate and clean the extracted data."""
        # Ensure all required fields are present
        required_fields = ["date", "location", "arrival_time", "departure_time", "operation_type"]
        for field in required_fields:
            if field not in data or data[field] is None or data[field] == "":
                if field == "date":
                    data[field] = datetime.now().strftime("%Y-%m-%d")
                elif field == "location":
                    data[field] = "Unknown Location"
                elif field == "arrival_time":
                    data[field] = "08:00"
                elif field == "departure_time":
                    data[field] = "12:00"
                elif field == "operation_type":
                    data[field] = "combined"
                    
        # Ensure lists are properly initialized
        list_fields = ["groups", "schedule", "tides"]
        for field in list_fields:
            if field not in data or not isinstance(data[field], list):
                data[field] = []
                
        # Ensure dictionaries are properly initialized
        dict_fields = ["equipment", "personnel"]
        for field in dict_fields:
            if field not in data or not isinstance(data[field], dict):
                if field == "equipment":
                    data[field] = {"zodiacs": 0, "twins": 0, "other": []}
                elif field == "personnel":
                    data[field] = {"total_count": 0, "guides": [], "drivers": []}
                    
        # Validate date format
        if "date" in data and data["date"]:
            try:
                # Try to convert to standard format if needed
                import re
                from datetime import datetime
                
                # If it's already in YYYY-MM-DD format, leave it
                if re.match(r'^\d{4}-\d{2}-\d{2}$', data["date"]):
                    pass
                # Try to parse other formats
                elif re.match(r'^\d{1,2}/\d{1,2}/\d{4}$', data["date"]):
                    # MM/DD/YYYY or DD/MM/YYYY
                    parts = data["date"].split('/')
                    if int(parts[0]) <= 12 and int(parts[1]) <= 31:
                        # Assume MM/DD/YYYY
                        data["date"] = f"{parts[2]}-{parts[0].zfill(2)}-{parts[1].zfill(2)}"
                    else:
                        # Assume DD/MM/YYYY
                        data["date"] = f"{parts[2]}-{parts[1].zfill(2)}-{parts[0].zfill(2)}"
                else:
                    # Try natural language parsing
                    from dateutil import parser
                    try:
                        parsed_date = parser.parse(data["date"], fuzzy=True)
                        data["date"] = parsed_date.strftime("%Y-%m-%d")
                    except:
                        # Fall back to current date
                        data["date"] = datetime.now().strftime("%Y-%m-%d")
            except:
                # If all parsing fails, use current date
                data["date"] = datetime.now().strftime("%Y-%m-%d")
                
        # Validate time formats
        time_fields = ["arrival_time", "departure_time"]
        for field in time_fields:
            if field in data and data[field]:
                # Ensure HH:MM format
                import re
                if not re.match(r'^\d{1,2}:\d{2}$', data[field]):
                    # Try to extract time
                    time_match = re.search(r'(\d{1,2}):(\d{2})', data[field])
                    if time_match:
                        data[field] = f"{time_match.group(1)}:{time_match.group(2)}"
                    else:
                        # Default times
                        data[field] = "08:00" if field == "arrival_time" else "12:00"
                        
        # Ensure operation_type is valid
        valid_types = ["am_only", "pm_only", "combined"]
        if "operation_type" in data and data["operation_type"] not in valid_types:
            # Determine based on times
            if "arrival_time" in data and "departure_time" in data:
                try:
                    arrival_hour = int(data["arrival_time"].split(":")[0])
                    departure_hour = int(data["departure_time"].split(":")[0])
                    
                    if arrival_hour < 12 and departure_hour < 12:
                        data["operation_type"] = "am_only"
                    elif arrival_hour >= 12 and departure_hour >= 12:
                        data["operation_type"] = "pm_only"
                    else:
                        data["operation_type"] = "combined"
                except:
                    data["operation_type"] = "combined"
            else:
                data["operation_type"] = "combined"
                
        return data

        return StructuredTool.from_function(
            func=map_expedition_data,
            name="data_mapper",
            description="Map raw text to structured expedition template with focused data extraction",
        )

    def _create_basic_extraction_from_text(self, raw_text: str, location: str = "") -> str:
        """Create basic extraction using regex patterns when LLM fails."""
        import re

        # Extract basic patterns
        times = re.findall(r'\b\d{1,2}:\d{2}\b', raw_text)
        dates = re.findall(r'\b\d{1,2}(?:st|nd|rd|th)?\s+\w+\s+\d{4}\b', raw_text)

        # Extract groups
        groups = []
        group_colors = ['Yellow', 'Blue', 'Red', 'Green']
        for color in group_colors:
            if color.lower() in raw_text.lower():
                groups.append({
                    "groupName": f"{color} Group",
                    "color": color,
                    "departureTime": times[0] if times else "Not specified",
                    "returnTime": times[-1] if len(times) > 1 else "Not specified",
                    "activity": "Zodiac tour"
                })

        # Extract equipment numbers
        zodiac_match = re.search(r'(\d+)\s*zodiac', raw_text, re.IGNORECASE)
        twin_match = re.search(r'(\d+)\s*twin', raw_text, re.IGNORECASE)

        # Build schedule from times
        schedule = []
        for i, time in enumerate(times[:5]):  # Limit to first 5 times
            schedule.append({
                "time": time,
                "type": "activity",
                "description": f"Scheduled activity {i+1}",
                "location": location or "Not specified"
            })

        result = {
            "date": dates[0] if dates else "2024-07-22",
            "location": location or "King George River",
            "arrival_time": times[0] if times else "Not specified",
            "departure_time": times[-1] if len(times) > 1 else "Not specified",
            "operation_type": "combined",
            "groups": groups,
            "schedule": schedule,
            "tides": [],
            "equipment": {
                "zodiacs": int(zodiac_match.group(1)) if zodiac_match else 0,
                "twins": int(twin_match.group(1)) if twin_match else 0,
                "other": []
            },
            "personnel": {"total_count": 0, "guides": [], "drivers": []},
            "notes": raw_text[:200] + "..." if len(raw_text) > 200 else raw_text,
            "weather": "Not specified"
        }

        return json.dumps(result, indent=2)

    def _create_pattern_recognition_tool(self) -> StructuredTool:
        """Create pattern recognition tool."""

        def recognize_patterns(extracted_data: str) -> str:
            """Analyze extracted data for operational patterns."""
            try:
                pattern_prompt = self._create_pattern_analysis_prompt(extracted_data)
                response = self.llm.invoke(pattern_prompt)
                return response
            except Exception as e:
                return f"Error recognizing patterns: {e!s}"

        return StructuredTool.from_function(
            func=recognize_patterns,
            name="pattern_recognizer",
            description="Analyze expedition data for operational patterns and timing insights",
        )

    def _create_enhanced_data_mapping_prompt(self, raw_text: str, location: str = "") -> str:
        """Create enhanced structured prompt for data mapping with better context and examples."""
        # Analyze text for key patterns first
        text_analysis = self._analyze_text_patterns(raw_text)

        return f"""You are an expert expedition data extraction specialist. Your task is to extract structured data from the expedition document text below and return it as valid JSON.

DOCUMENT TEXT TO ANALYZE:
{raw_text}

LOCATION CONTEXT: {location if location else "Extract from document"}

TEXT ANALYSIS HINTS: {text_analysis}

EXTRACTION RULES:
1. Extract the actual date from the document (look for dates like "Tuesday 22nd July 2024")
2. Extract the actual location name (like "King George River", "Koolama Bay")
3. Find all group mentions (Yellow, Blue, Red, Green groups)
4. Extract all times and activities from the schedule
5. Find tide information if present
6. Count equipment (zodiacs, twins) mentioned
7. Extract operational notes and weather conditions

IMPORTANT: Extract data from the ACTUAL DOCUMENT TEXT above, not from examples or templates.

Return ONLY valid JSON in this exact format:
{{
    "date": "YYYY-MM-DD",
    "location": "actual location from document",
    "arrival_time": "HH:MM",
    "departure_time": "HH:MM",
    "operation_type": "combined",
    "groups": [
        {{
            "groupName": "actual group name from document",
            "color": "actual color from document",
            "departureTime": "HH:MM from document",
            "returnTime": "HH:MM from document",
            "activity": "actual activity from document"
        }}
    ],
    "schedule": [
        {{
            "time": "HH:MM from document",
            "type": "arrival|departure|drop_zodiacs|activity|briefing",
            "description": "actual description from document",
            "location": "actual location from document"
        }}
    ],
    "tides": [
        {{
            "time": "HH:MM from document",
            "height": actual_number_from_document,
            "label": "high|low"
        }}
    ],
    "equipment": {{
        "zodiacs": actual_number_from_document,
        "twins": actual_number_from_document,
        "other": []
    }},
    "personnel": {{
        "total_count": 0,
        "guides": [],
        "drivers": []
    }},
    "notes": "actual notes from document",
    "weather": "actual weather from document or Not specified"
}}

CRITICAL: Return ONLY the JSON object with NO additional text, explanations, prefixes, or formatting."""

    def _analyze_text_patterns(self, text: str) -> str:
        """Analyze text for key patterns to provide hints to the LLM."""
        hints = []
        text_lower = text.lower()

        # Time patterns
        import re
        time_patterns = re.findall(r'\b\d{1,2}:\d{2}\b', text)
        if time_patterns:
            hints.append(f"Found times: {', '.join(time_patterns)}")

        # Date patterns
        date_patterns = re.findall(r'\b\d{1,2}[/-]\d{1,2}[/-]\d{2,4}\b', text)
        if date_patterns:
            hints.append(f"Found dates: {', '.join(date_patterns)}")

        # Location indicators
        location_keywords = ['bay', 'falls', 'reef', 'island', 'beach', 'creek', 'river']
        found_locations = [kw for kw in location_keywords if kw in text_lower]
        if found_locations:
            hints.append(f"Location keywords: {', '.join(found_locations)}")

        # Activity indicators
        activity_keywords = ['zodiac', 'hiking', 'wildlife', 'photography', 'snorkeling', 'cruise']
        found_activities = [kw for kw in activity_keywords if kw in text_lower]
        if found_activities:
            hints.append(f"Activity keywords: {', '.join(found_activities)}")

        # Group indicators
        group_keywords = ['red', 'blue', 'yellow', 'green', 'orange', 'purple', 'group']
        found_groups = [kw for kw in group_keywords if kw in text_lower]
        if found_groups:
            hints.append(f"Group keywords: {', '.join(found_groups)}")

        # Tide indicators
        tide_keywords = ['tide', 'high', 'low', 'ht', 'lt']
        found_tides = [kw for kw in tide_keywords if kw in text_lower]
        if found_tides:
            hints.append(f"Tide keywords: {', '.join(found_tides)}")

        return '; '.join(hints) if hints else "No specific patterns detected - extract any available information"

    def _invoke_llm_with_validation(self, prompt: str, max_retries: int = 1) -> str:
        """Invoke LLM with smart validation and controlled retry logic."""
        last_response = None
        retry_history = []

        for attempt in range(max_retries + 1):
            try:
                logger.debug(f"LLM invocation attempt {attempt + 1}/{max_retries + 1}")
                response = self.llm.invoke(prompt)
                response_stripped = response.strip()

                # Track retry history to detect circular behavior
                if response_stripped in retry_history:
                    logger.warning(f"Detected circular retry behavior - same response repeated")
                    break

                retry_history.append(response_stripped)
                last_response = response_stripped

                # Validate response quality
                validation_result = self._validate_llm_response_quality(response_stripped)

                if validation_result["is_valid"]:
                    logger.debug(f"LLM response validated successfully on attempt {attempt + 1}")
                    return response_stripped

                # If this is the last attempt, don't retry
                if attempt >= max_retries:
                    logger.warning(f"Max retries reached, using best available response")
                    break

                # Decide whether to retry based on the validation result
                if validation_result["should_retry"]:
                    logger.info(f"Retrying LLM invocation: {validation_result['reason']}")
                    # Modify prompt slightly to avoid identical responses
                    prompt = self._modify_prompt_for_retry(prompt, attempt + 1, validation_result)
                else:
                    logger.info(f"Not retrying: {validation_result['reason']}")
                    break

            except Exception as e:
                logger.error(f"LLM invocation failed on attempt {attempt + 1}: {e}")
                if attempt >= max_retries:
                    raise e
                continue

        # Return the best response we got, even if not perfect
        return last_response or "{}"

    def _validate_llm_response_quality(self, response: str) -> Dict[str, Any]:
        """Validate LLM response quality and determine if retry is needed."""
        import re

        # Check if response looks like JSON
        if response.startswith('{') and response.endswith('}'):
            try:
                # Try to parse as JSON
                json.loads(response)
                return {
                    "is_valid": True,
                    "should_retry": False,
                    "reason": "Valid JSON response"
                }
            except json.JSONDecodeError as e:
                # JSON-like but malformed
                return {
                    "is_valid": False,
                    "should_retry": True,
                    "reason": f"Malformed JSON: {str(e)[:100]}"
                }

        # Check if JSON is embedded in text
        json_match = re.search(r'\{.*\}', response, re.DOTALL)
        if json_match:
            try:
                json.loads(json_match.group(0))
                return {
                    "is_valid": True,
                    "should_retry": False,
                    "reason": "Valid JSON found in response text"
                }
            except json.JSONDecodeError:
                return {
                    "is_valid": False,
                    "should_retry": True,
                    "reason": "Embedded JSON is malformed"
                }

        # No JSON found
        if len(response) < 10:
            return {
                "is_valid": False,
                "should_retry": True,
                "reason": "Response too short"
            }

        # Response doesn't contain JSON but might be usable
        return {
            "is_valid": False,
            "should_retry": False,
            "reason": "No JSON found, but response has content"
        }

    def _modify_prompt_for_retry(self, original_prompt: str, attempt: int, validation_result: Dict[str, Any]) -> str:
        """Modify prompt for retry to avoid circular behavior."""
        # Add specific guidance based on the validation failure
        retry_guidance = ""

        if "Malformed JSON" in validation_result["reason"]:
            retry_guidance = "\n\nIMPORTANT: Ensure your JSON is properly formatted with correct quotes, commas, and brackets. Do not include any text outside the JSON object."
        elif "No JSON found" in validation_result["reason"]:
            retry_guidance = "\n\nIMPORTANT: You must return ONLY a valid JSON object. Start with { and end with }. Do not include any explanatory text."
        elif "Response too short" in validation_result["reason"]:
            retry_guidance = "\n\nIMPORTANT: Provide a complete JSON response with all required fields filled out."

        # Add attempt-specific variation to avoid identical responses
        retry_guidance += f"\n\nThis is attempt {attempt + 1}. Please provide a different, valid JSON response."

        return original_prompt + retry_guidance

    def _validate_and_clean_json_response(self, response: str, location: str = "") -> str:
        """Validate and clean JSON response from LLM with comprehensive error recovery."""
        # Use the comprehensive JSON validator
        validation_result = validate_and_repair_json(response, f"LLM response for location: {location}")

        if validation_result["is_valid"] and validation_result["repaired_json"]:
            logger.info(f"JSON successfully validated and repaired using {validation_result['strategy_used']}")

            # Ensure required fields exist with defaults
            parsed = self._ensure_complete_structure(validation_result["repaired_json"], location)
            # Validate and clean specific fields
            parsed = self._clean_extracted_fields(parsed)
            # Return clean JSON
            return json.dumps(parsed, indent=2, ensure_ascii=False)

        # Validation failed, log details and return default structure
        logger.warning(f"JSON validation failed: {validation_result['errors']}")
        if validation_result["warnings"]:
            logger.info(f"JSON validation warnings: {validation_result['warnings']}")

        return self._create_default_json_structure(location, f"JSON validation failed: {validation_result['errors']}")



    def _create_default_json_structure(self, location: str = "", error_msg: str = "") -> str:
        """Create a valid default JSON structure."""
        return json.dumps({
            "date": datetime.now().strftime("%Y-%m-%d"),
            "location": location or "Not specified",
            "arrival_time": "Not specified",
            "departure_time": "Not specified",
            "operation_type": "unknown",
            "groups": [],
            "schedule": [],
            "tides": [],
            "equipment": {"zodiacs": 0, "twins": 0, "other": []},
            "personnel": {"total_count": 0, "guides": [], "drivers": []},
            "notes": error_msg or "Default structure - parsing failed",
            "weather": "Not specified",
            "extraction_method": "default_fallback"
        }, indent=2, ensure_ascii=False)

    def _clean_extracted_fields(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Clean and validate extracted fields."""
        # Clean time fields
        for time_field in ['arrival_time', 'departure_time']:
            if time_field in data and data[time_field]:
                data[time_field] = self._normalize_time(data[time_field])

        # Clean date field
        if 'date' in data and data['date']:
            data['date'] = self._normalize_date(data['date'])

        # Ensure groups is a list
        if not isinstance(data.get('groups'), list):
            data['groups'] = []

        # Ensure schedule is a list
        if not isinstance(data.get('schedule'), list):
            data['schedule'] = []

        # Ensure tides is a list
        if not isinstance(data.get('tides'), list):
            data['tides'] = []

        # Ensure equipment is a dict
        if not isinstance(data.get('equipment'), dict):
            data['equipment'] = {"zodiacs": 0, "twins": 0, "other": []}

        # Ensure personnel is a dict
        if not isinstance(data.get('personnel'), dict):
            data['personnel'] = {"total_count": 0, "guides": [], "drivers": []}

        return data

    def _normalize_time(self, time_str: str) -> str:
        """Normalize time string to HH:MM format."""
        if not time_str or time_str in ["Not specified", "", "unknown"]:
            return "Not specified"

        import re
        # Try to extract time pattern
        time_match = re.search(r'(\d{1,2}):(\d{2})', str(time_str))
        if time_match:
            hour, minute = time_match.groups()
            return f"{int(hour):02d}:{minute}"

        return str(time_str)

    def _normalize_date(self, date_str: str) -> str:
        """Normalize date string to YYYY-MM-DD format."""
        if not date_str or date_str in ["Not specified", "", "unknown"]:
            return datetime.now().strftime("%Y-%m-%d")

        import re
        # Try various date formats
        date_patterns = [
            r'(\d{4})-(\d{1,2})-(\d{1,2})',  # YYYY-MM-DD
            r'(\d{1,2})/(\d{1,2})/(\d{4})',  # MM/DD/YYYY
            r'(\d{1,2})-(\d{1,2})-(\d{4})',  # MM-DD-YYYY
        ]

        for pattern in date_patterns:
            match = re.search(pattern, str(date_str))
            if match:
                if pattern.startswith(r'(\d{4})'):  # YYYY-MM-DD
                    year, month, day = match.groups()
                else:  # MM/DD/YYYY or MM-DD-YYYY
                    month, day, year = match.groups()

                try:
                    return f"{int(year):04d}-{int(month):02d}-{int(day):02d}"
                except ValueError:
                    pass

        return datetime.now().strftime("%Y-%m-%d")

    def _execute_agent_with_fallback(self, query: str, raw_text: str, location: str = "") -> Dict[str, Any]:
        """Execute agent with comprehensive fallback handling and circular retry prevention."""
        execution_attempts = []

        try:
            # Primary attempt with agent
            logger.info("Attempting primary agent execution")

            # Check for circular behavior in the query
            if self._is_circular_query(query, execution_attempts):
                logger.warning("Detected potential circular query, using direct fallback")
                return self._fallback_direct_extraction(raw_text, location)

            execution_attempts.append(query)
            result = self.agent_executor.invoke({"input": query})

            # Validate the result quality
            validation = self._validate_agent_result(result)

            if validation["is_valid"]:
                logger.info("Agent execution successful")
                return result
            elif validation["should_fallback"]:
                logger.warning(f"Agent result validation failed: {validation['reason']}, using fallback")
                return self._fallback_direct_extraction(raw_text, location)
            else:
                # Return the result even if not perfect
                logger.info(f"Agent result has issues but is usable: {validation['reason']}")
                return result

        except Exception as e:
            error_msg = str(e)

            # Check for specific error patterns that indicate circular behavior
            if any(pattern in error_msg.lower() for pattern in [
                "maximum iterations", "timeout", "parsing error", "invalid format"
            ]):
                logger.warning(f"Agent execution failed with circular behavior indicators: {error_msg}")
            else:
                logger.error(f"Agent execution failed: {e}")

            logger.info("Attempting direct extraction fallback")
            return self._fallback_direct_extraction(raw_text, location)

    def _is_circular_query(self, query: str, previous_attempts: List[str]) -> bool:
        """Check if the query might lead to circular behavior."""
        # Check if we've seen this exact query before
        if query in previous_attempts:
            return True

        # Check if the query is very similar to previous attempts
        for prev_query in previous_attempts:
            if self._queries_are_similar(query, prev_query):
                return True

        return False

    def _queries_are_similar(self, query1: str, query2: str, threshold: float = 0.8) -> bool:
        """Check if two queries are similar enough to indicate circular behavior."""
        # Simple similarity check based on common words
        words1 = set(query1.lower().split())
        words2 = set(query2.lower().split())

        if not words1 or not words2:
            return False

        intersection = words1.intersection(words2)
        union = words1.union(words2)

        similarity = len(intersection) / len(union) if union else 0
        return similarity >= threshold

    def _validate_agent_result(self, result: Dict[str, Any]) -> Dict[str, Any]:
        """Validate agent execution result quality."""
        if not result:
            return {
                "is_valid": False,
                "should_fallback": True,
                "reason": "Empty result from agent"
            }

        # Check for expected output format
        if "output" in result:
            output = result["output"]
            if isinstance(output, str) and len(output.strip()) > 10:
                return {
                    "is_valid": True,
                    "should_fallback": False,
                    "reason": "Valid output found"
                }

        # Check for alternative result formats
        if "result" in result:
            return {
                "is_valid": True,
                "should_fallback": False,
                "reason": "Valid result found in alternative format"
            }

        # Check intermediate steps for useful information
        if "intermediate_steps" in result and result["intermediate_steps"]:
            return {
                "is_valid": False,
                "should_fallback": False,
                "reason": "No direct output but has intermediate steps"
            }

        return {
            "is_valid": False,
            "should_fallback": True,
            "reason": "No usable output found in agent result"
        }

    def _fallback_direct_extraction(self, raw_text: str, location: str = "") -> Dict[str, Any]:
        """Fallback method for direct extraction when agent fails."""
        try:
            logger.info("Using direct LLM extraction as fallback")

            # Use the enhanced data mapping prompt directly
            mapping_prompt = self._create_enhanced_data_mapping_prompt(raw_text, location)

            # Get response with validation
            response = self._invoke_llm_with_validation(mapping_prompt)

            # Validate and clean the response
            validated_response = self._validate_and_clean_json_response(response, location)

            return {
                "output": validated_response,
                "method": "direct_fallback",
                "success": True
            }

        except Exception as e:
            logger.error(f"Fallback extraction also failed: {e}")
            # Return minimal valid structure
            return {
                "output": json.dumps({
                    "date": datetime.now().strftime("%Y-%m-%d"),
                    "location": location or "Not specified",
                    "arrival_time": "Not specified",
                    "departure_time": "Not specified",
                    "operation_type": "unknown",
                    "groups": [],
                    "schedule": [],
                    "tides": [],
                    "equipment": {"zodiacs": 0, "twins": 0, "other": []},
                    "personnel": {"total_count": 0, "guides": [], "drivers": []},
                    "notes": f"Extraction failed: {e}",
                    "weather": "Not specified",
                    "extraction_method": "emergency_fallback"
                }, indent=2),
                "method": "emergency_fallback",
                "success": False,
                "error": str(e)
            }

    def _create_pattern_analysis_prompt(self, extracted_data: str) -> str:
        """Create prompt for pattern analysis."""
        return f"""
Analyze the following extracted expedition data for operational patterns and insights.

EXTRACTED DATA:
{extracted_data}

Identify and explain these patterns:
1. TIMING PATTERNS: Why was this operation scheduled at this time?
   - ETA 06:00-08:00 usually indicates all-day operations
   - ETA 12:00+ usually indicates PM-only operations
   - Multiple groups suggest extended operations

2. EQUIPMENT PATTERNS: Equipment allocation reasoning
   - Number of zodiacs vs group size
   - Twin boat usage patterns
   - Equipment-to-activity relationships

3. OPERATIONAL PATTERNS: Activity type decisions
   - Landing vs cruise operations
   - Duration patterns (2h, 4h, all-day)
   - Group rotation strategies

4. ENVIRONMENTAL FACTORS: Tide and weather influences
   - Tide timing impact on operations
   - Weather-dependent activity choices

Return analysis as structured text explaining the reasoning behind operational decisions.
"""

    def extract_from_documents(
        self, file_paths: List[str], location: str = ""
    ) -> Dict[str, Any]:
        """
        Extract structured data from expedition documents.

        Args:
            file_paths: List of document file paths to process
            location: Optional location context for better extraction

        Returns:
            Dictionary containing extracted data and metadata
        """
        try:
            logger.info(f"Starting extraction from {len(file_paths)} documents")

            # Extract text from all documents
            all_text = []
            processed_files = []

            for file_path in file_paths:
                text = self.text_extractor.extract_text(file_path)
                if text:
                    all_text.append(text)
                    processed_files.append(file_path)
                    logger.info(f"Successfully extracted text from: {file_path}")
                else:
                    logger.warning(f"Failed to extract text from: {file_path}")

            if not all_text:
                return {
                    "success": False,
                    "error": "No text could be extracted from any documents",
                    "processed_files": [],
                }

            # Combine all text
            combined_text = "\n\n--- DOCUMENT SEPARATOR ---\n\n".join(all_text)

            # Use agent to extract structured data with robust error handling
            extraction_query = f"""
            Extract structured expedition data from the provided documents for location: {location}

            Process the following combined document text and return structured JSON data:

            {combined_text[:6000]}  # Increased limit for better context
            """

            result = self._execute_agent_with_fallback(extraction_query, combined_text, location)

            # Parse the result
            extracted_data = self._parse_extraction_result(result, location)

            return {
                "success": True,
                "extracted_data": extracted_data,
                "processed_files": processed_files,
                "extraction_timestamp": datetime.now().isoformat(),
                "location": location,
            }

        except Exception as e:
            logger.error(f"Error in document extraction: {e}")
            return {"success": False, "error": str(e), "processed_files": []}

    def _parse_extraction_result(
        self, result: Dict[str, Any], location: str = "Unknown"
    ) -> Dict[str, Any]:
        """Parse the agent execution result to extract structured data with comprehensive error handling."""
        try:
            # Handle different result formats
            output_text = ""
            if isinstance(result, dict):
                if "output" in result:
                    output_text = result["output"]
                elif "result" in result:
                    output_text = result["result"]
                elif "final_answer" in result:
                    output_text = result["final_answer"]
                else:
                    # Try to find any string value in the result
                    for key, value in result.items():
                        if isinstance(value, str) and len(value) > 10:
                            output_text = value
                            break
            else:
                output_text = str(result)

            # Try multiple JSON extraction strategies
            import re

            # Strategy 1: Look for complete JSON object
            json_match = re.search(r'\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}', output_text, re.DOTALL)
            if json_match:
                json_str = json_match.group()
                try:
                    parsed_data = json.loads(json_str)
                    # Validate it has expected structure
                    if isinstance(parsed_data, dict) and any(key in parsed_data for key in ['date', 'location', 'groups']):
                        logger.info("Successfully parsed JSON from agent output")
                        return self._ensure_complete_structure(parsed_data, location)
                except json.JSONDecodeError as e:
                    logger.warning(f"JSON parsing failed for strategy 1: {e}")

            # Strategy 2: Look for JSON-like content and try to fix it
            json_like = re.search(r'\{.*\}', output_text, re.DOTALL)
            if json_like:
                json_str = json_like.group()
                try:
                    # Try to fix common JSON issues
                    fixed_json = self._fix_common_json_issues(json_str)
                    parsed_data = json.loads(fixed_json)
                    logger.info("Successfully parsed fixed JSON from agent output")
                    return self._ensure_complete_structure(parsed_data, location)
                except json.JSONDecodeError as e:
                    logger.warning(f"JSON parsing failed for strategy 2: {e}")

            # Strategy 3: Fallback to text analysis
            logger.warning("Could not parse JSON, using fallback structure with text analysis")
            return self._create_intelligent_fallback_structure(output_text, location)

        except Exception as e:
            logger.error(f"Error parsing extraction result: {e}")
            return self._create_empty_structure(location)

    def _fix_common_json_issues(self, json_str: str) -> str:
        """Fix common JSON formatting issues with comprehensive sanitization."""
        import re

        # Step 1: Remove control characters and non-printable characters
        json_str = self._sanitize_control_characters(json_str)

        # Step 2: Fix quote issues
        json_str = self._fix_quote_issues(json_str)

        # Step 3: Fix structural issues
        json_str = self._fix_structural_issues(json_str)

        # Step 4: Validate and repair JSON structure
        json_str = self._repair_json_structure(json_str)

        return json_str

    def _sanitize_control_characters(self, text: str) -> str:
        """Remove or replace control characters that break JSON."""
        import re

        # Remove null bytes and other problematic control characters
        text = text.replace('\x00', '')  # Null byte
        text = text.replace('\x08', '')  # Backspace
        text = text.replace('\x0c', '')  # Form feed
        text = text.replace('\x0b', '')  # Vertical tab

        # Replace problematic characters with safe alternatives
        text = text.replace('\r\n', '\\n')  # Windows line endings
        text = text.replace('\r', '\\n')    # Mac line endings
        text = text.replace('\n', '\\n')    # Unix line endings
        text = text.replace('\t', '\\t')    # Tabs

        # Remove other control characters (except allowed ones)
        text = re.sub(r'[\x00-\x08\x0b\x0c\x0e-\x1f\x7f]', '', text)

        return text

    def _fix_quote_issues(self, text: str) -> str:
        """Fix quote-related JSON issues."""
        import re

        # Fix single quotes to double quotes (but be careful with contractions)
        # First, protect contractions and possessives
        text = re.sub(r"(\w)'(s|t|re|ve|ll|d)\b", r"\1APOSTROPHE\2", text)

        # Replace remaining single quotes with double quotes
        text = text.replace("'", '"')

        # Restore contractions
        text = text.replace('APOSTROPHE', "'")

        # Fix escaped quotes in strings
        text = re.sub(r'\\"+', '\\"', text)  # Fix multiple escaped quotes

        # Fix unescaped quotes inside strings
        # This is complex, so we'll use a more conservative approach
        text = self._fix_unescaped_quotes_in_strings(text)

        return text

    def _fix_unescaped_quotes_in_strings(self, text: str) -> str:
        """Fix unescaped quotes inside JSON strings."""
        import re

        # Pattern to find strings and fix quotes inside them
        def fix_string_quotes(match):
            string_content = match.group(1)
            # Escape any unescaped quotes inside the string
            fixed_content = string_content.replace('\\"', 'TEMP_ESCAPED_QUOTE')
            fixed_content = fixed_content.replace('"', '\\"')
            fixed_content = fixed_content.replace('TEMP_ESCAPED_QUOTE', '\\"')
            return f'"{fixed_content}"'

        # Find and fix quoted strings
        text = re.sub(r'"([^"\\]*(?:\\.[^"\\]*)*)"', fix_string_quotes, text)

        return text

    def _fix_structural_issues(self, text: str) -> str:
        """Fix structural JSON issues."""
        import re

        # Remove trailing commas before closing brackets/braces
        text = re.sub(r',(\s*[}\]])', r'\1', text)

        # Fix unquoted keys (but be careful not to break quoted strings)
        text = re.sub(r'(\s*)(\w+)(\s*):', r'\1"\2"\3:', text)

        # Fix missing commas between array/object elements
        text = re.sub(r'}\s*{', '}, {', text)  # Between objects
        text = re.sub(r']\s*\[', '], [', text)  # Between arrays
        text = re.sub(r'"\s*"', '", "', text)   # Between strings (be careful)

        # Fix missing quotes around string values
        text = self._fix_unquoted_string_values(text)

        return text

    def _fix_unquoted_string_values(self, text: str) -> str:
        """Fix unquoted string values in JSON."""
        import re

        # Pattern to find unquoted string values (this is tricky)
        # Look for key: value patterns where value is not quoted, number, boolean, or null
        def fix_unquoted_value(match):
            key = match.group(1)
            value = match.group(2).strip()

            # Don't quote numbers, booleans, null, or already quoted strings
            if (value.startswith('"') or
                value in ['true', 'false', 'null'] or
                re.match(r'^-?\d+(\.\d+)?$', value) or
                value.startswith('[') or value.startswith('{')):
                return match.group(0)

            # Quote the value
            return f'{key}: "{value}"'

        # Apply the fix
        text = re.sub(r'("[\w\s]+"):\s*([^",\[\]{}]+)(?=[,}])', fix_unquoted_value, text)

        return text

    def _repair_json_structure(self, text: str) -> str:
        """Attempt to repair JSON structure issues."""
        import re

        # Ensure the text starts and ends with braces
        text = text.strip()
        if not text.startswith('{'):
            # Find the first opening brace
            match = re.search(r'\{', text)
            if match:
                text = text[match.start():]
            else:
                # No opening brace found, wrap the content
                text = '{' + text + '}'

        if not text.endswith('}'):
            # Find the last closing brace
            last_brace = text.rfind('}')
            if last_brace != -1:
                text = text[:last_brace + 1]
            else:
                # No closing brace found, add one
                text = text + '}'

        # Balance braces and brackets
        text = self._balance_brackets(text)

        return text

    def _balance_brackets(self, text: str) -> str:
        """Balance brackets and braces in JSON."""
        # Count and balance braces
        open_braces = text.count('{')
        close_braces = text.count('}')

        if open_braces > close_braces:
            text += '}' * (open_braces - close_braces)
        elif close_braces > open_braces:
            # Remove excess closing braces from the end
            excess = close_braces - open_braces
            for _ in range(excess):
                last_brace = text.rfind('}')
                if last_brace != -1:
                    text = text[:last_brace] + text[last_brace + 1:]

        # Count and balance brackets
        open_brackets = text.count('[')
        close_brackets = text.count(']')

        if open_brackets > close_brackets:
            text += ']' * (open_brackets - close_brackets)
        elif close_brackets > open_brackets:
            # Remove excess closing brackets from the end
            excess = close_brackets - open_brackets
            for _ in range(excess):
                last_bracket = text.rfind(']')
                if last_bracket != -1:
                    text = text[:last_bracket] + text[last_bracket + 1:]

        return text

    def _ensure_complete_structure(self, data: Dict[str, Any], location: str = "") -> Dict[str, Any]:
        """Ensure the parsed data has all required fields."""
        defaults = {
            "date": datetime.now().strftime("%Y-%m-%d"),
            "location": location or "Not specified",
            "arrival_time": "Not specified",
            "departure_time": "Not specified",
            "operation_type": "unknown",
            "groups": [],
            "schedule": [],
            "tides": [],
            "equipment": {"zodiacs": 0, "twins": 0, "other": []},
            "personnel": {"total_count": 0, "guides": [], "drivers": []},
            "notes": "Extracted from document",
            "weather": "Not specified"
        }

        # Merge with defaults
        for key, default_value in defaults.items():
            if key not in data or data[key] in ["Not specified", "", None, "unknown"]:
                data[key] = default_value

        return data

    def _create_intelligent_fallback_structure(self, text: str, location: str = "") -> Dict[str, Any]:
        """Create fallback structure with intelligent text analysis."""
        # Try to extract some basic information from text
        import re

        # Look for times
        times = re.findall(r'\b\d{1,2}:\d{2}\b', text)

        # Look for dates
        dates = re.findall(r'\b\d{1,2}[/-]\d{1,2}[/-]\d{2,4}\b', text)

        # Look for group mentions
        groups = []
        group_colors = ['red', 'blue', 'yellow', 'green', 'orange', 'purple']
        for color in group_colors:
            if color.lower() in text.lower():
                groups.append({
                    "groupName": f"{color.title()} Group",
                    "color": color.lower(),
                    "departureTime": times[0] if times else "Not specified",
                    "returnTime": times[-1] if len(times) > 1 else "Not specified",
                    "activity": "Activity extracted from text"
                })

        return {
            "date": dates[0] if dates else datetime.now().strftime("%Y-%m-%d"),
            "location": location or "Extracted from text",
            "arrival_time": times[0] if times else "Not specified",
            "departure_time": times[-1] if len(times) > 1 else "Not specified",
            "operation_type": "combined" if len(times) > 2 else "unknown",
            "groups": groups,
            "schedule": [{"time": t, "type": "activity", "description": "Extracted from text", "location": location} for t in times[:3]],
            "tides": [],
            "equipment": {"zodiacs": 0, "twins": 0, "other": []},
            "personnel": {"total_count": 0, "guides": [], "drivers": []},
            "notes": text[:500] + "..." if len(text) > 500 else text,
            "weather": "Not specified",
            "extraction_method": "intelligent_fallback"
        }

    def _create_fallback_structure(self, text: str) -> Dict[str, Any]:
        """Create fallback structure when JSON parsing fails."""
        return {
            "date": datetime.now().strftime("%Y-%m-%d"),
            "location": "Unknown",
            "operation_type": "combined",
            "groups": [],
            "schedule": [],
            "tides": [],
            "equipment": {"zodiacs": 0, "twins": 0, "other": []},
            "personnel": {"total_count": 0, "guides": [], "drivers": []},
            "notes": text[:500] + "..." if len(text) > 500 else text,
            "extraction_method": "fallback",
        }

    def _create_empty_structure(self, location: str = "Unknown") -> Dict[str, Any]:
        """Create empty structure for failed extractions."""
        return {
            "date": datetime.now().strftime("%Y-%m-%d"),
            "location": location,
            "arrival_time": "Not specified",
            "departure_time": "Not specified",
            "operation_type": "unknown",
            "groups": [],
            "schedule": [],
            "tides": [],
            "equipment": {"zodiacs": 0, "twins": 0, "other": []},
            "personnel": {"total_count": 0, "guides": [], "drivers": []},
            "notes": "Extraction failed",
            "weather": "Not specified",
            "extraction_method": "empty",
        }

    def extract_single_document(
        self, file_path: str, location: str = ""
    ) -> Dict[str, Any]:
        """
        Extract data from a single document.

        Args:
            file_path: Path to the document file
            location: Optional location context

        Returns:
            Dictionary containing extracted data
        """
        return self.extract_from_documents([file_path], location)

    def extract_from_document(self, file_path: str) -> Dict[str, Any]:
        """
        Extract structured data from a single expedition document.

        Args:
            file_path: Path to the expedition document

        Returns:
            Dictionary containing extracted structured data
        """
        try:
            logger.info(f"Extracting data from document: {file_path}")

            input_text = f"""
            Extract structured expedition data from this document: {file_path}
            
            Please process the document and extract the following information:
            
            1. Basic Information:
               - Date of operation (YYYY-MM-DD format)
               - Location name
               - Operation type (AM-only, PM-only, or combined)
            
            2. Timing Information:
               - Arrival/start time
               - Departure/end time
               - Operation duration
            
            3. Activities:
               - List of all activities performed
               - Activity types (landing, zodiac cruise, etc.)
            
            4. Tides:
               - Tide times and heights
               - High/low tide classifications
            
            5. Personnel:
               - Total personnel count
               - Roles and assignments
               - Team member names if available
            
            6. Equipment:
               - List of equipment used
               - Special equipment notes
            
            7. Safety:
               - Safety incidents or observations
               - Risk assessments
               - Safety equipment used
            
            8. Weather:
               - Weather conditions
               - Impact on operations
            
            9. Notes:
               - Operational notes
               - Setup requirements
               - Special considerations
            
            Return the extracted data as a valid JSON object with all the above sections.
            """

            result = self.agent_executor.invoke({"input": input_text})

            # Parse and validate the extracted data
            extracted_data = self._parse_extraction_result(result, file_path)

            logger.info(f"Data extraction completed for: {file_path}")
            return extracted_data

        except Exception as e:
            logger.error(f"Error extracting data from {file_path}: {e}")
            return {
                "success": False,
                "error": str(e),
                "file_path": file_path,
                "extracted_data": {},
            }

    def extract_from_document_group(
        self, document_paths: List[str], location: str
    ) -> Dict[str, Any]:
        """
        Extract and consolidate data from a group of documents for the same location.

        Args:
            document_paths: List of document file paths
            location: Location name for the document group

        Returns:
            Dictionary containing consolidated extracted data
        """
        try:
            logger.info(f"Extracting data from document group for location: {location}")

            all_extracted_data = []

            # Extract data from each document
            for doc_path in document_paths:
                doc_data = self.extract_from_document(doc_path)
                if doc_data.get("success"):
                    all_extracted_data.append(doc_data["extracted_data"])

            # Consolidate the data
            consolidated_data = self._consolidate_group_data(
                all_extracted_data, location
            )

            logger.info(
                f"Group data extraction completed for {len(document_paths)} documents"
            )
            return {
                "success": True,
                "location": location,
                "document_count": len(document_paths),
                "processed_documents": len(all_extracted_data),
                "consolidated_data": consolidated_data,
            }

        except Exception as e:
            logger.error(f"Error extracting group data for {location}: {e}")
            return {
                "success": False,
                "error": str(e),
                "location": location,
                "consolidated_data": {},
            }

    def validate_extracted_data(self, extracted_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate extracted data against expedition format requirements.

        Args:
            extracted_data: The extracted data to validate

        Returns:
            Dictionary containing validation results and any corrections
        """
        try:
            logger.info("Validating extracted data")

            input_text = f"""
            Validate this extracted expedition data against the expedition format specification:
            
            {json.dumps(extracted_data, indent=2)}
            
            Check for:
            1. Required fields presence
            2. Data format correctness (dates, times, etc.)
            3. Value ranges and constraints
            4. Data consistency
            5. Missing or incomplete information
            
            Provide validation results and suggest corrections for any issues found.
            Return results as JSON with validation_passed (boolean), issues (list), and corrected_data (object).
            """

            result = self.agent_executor.invoke({"input": input_text})

            return self._parse_validation_result(result, extracted_data)

        except Exception as e:
            logger.error(f"Error validating extracted data: {e}")
            return {
                "validation_passed": False,
                "error": str(e),
                "issues": [f"Validation error: {e!s}"],
                "corrected_data": extracted_data,
            }



    def _extract_json_from_text(self, text: str) -> Optional[Dict[str, Any]]:
        """Extract JSON data from text output."""
        try:
            # Look for JSON blocks in the text
            import re

            # Try to find JSON between curly braces
            json_pattern = r"\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}"
            matches = re.findall(json_pattern, text, re.DOTALL)

            for match in matches:
                try:
                    return json.loads(match)
                except json.JSONDecodeError:
                    continue

            # Try to parse the entire text as JSON
            return json.loads(text)

        except Exception:
            return None

    def _create_structured_data_from_text(self, text: str) -> Dict[str, Any]:
        """Create structured data from text when JSON parsing fails."""
        # This is a fallback method to extract basic information from text
        structured_data = {
            "date": "",
            "location": "",
            "activities": [],
            "tides": [],
            "personnel": {"total": 0, "roles": []},
            "equipment": [],
            "safety": {"incidents": [], "observations": []},
            "timing": {"start_time": "", "end_time": "", "duration": ""},
            "notes": text,  # Store the full text as notes
            "operation_type": "unknown",
        }

        # Simple text parsing to extract basic information
        lines = text.lower().split("\n")

        for line in lines:
            # Look for date patterns
            import re

            date_match = re.search(r"\d{4}-\d{2}-\d{2}", line)
            if date_match and not structured_data["date"]:
                structured_data["date"] = date_match.group()

            # Look for time patterns
            time_match = re.search(r"\d{1,2}:\d{2}", line)
            if time_match and not structured_data["timing"]["start_time"]:
                structured_data["timing"]["start_time"] = time_match.group()

        return structured_data

    def _consolidate_group_data(
        self, data_list: List[Dict[str, Any]], location: str
    ) -> Dict[str, Any]:
        """Consolidate data from multiple documents for the same location."""
        if not data_list:
            return {}

        # Start with the first document's data as base
        consolidated = data_list[0].copy() if data_list else {}
        consolidated["location"] = location

        # Merge data from other documents
        for data in data_list[1:]:
            # Merge activities
            if "activities" in data:
                existing_activities = set(consolidated.get("activities", []))
                new_activities = set(data["activities"])
                consolidated["activities"] = list(
                    existing_activities.union(new_activities)
                )

            # Merge equipment
            if "equipment" in data:
                existing_equipment = set(consolidated.get("equipment", []))
                new_equipment = set(data["equipment"])
                consolidated["equipment"] = list(
                    existing_equipment.union(new_equipment)
                )

            # Merge tides (keep all unique tide entries)
            if "tides" in data:
                existing_tides = consolidated.get("tides", [])
                for tide in data["tides"]:
                    if tide not in existing_tides:
                        existing_tides.append(tide)
                consolidated["tides"] = existing_tides

            # Merge notes
            if data.get("notes"):
                existing_notes = consolidated.get("notes", "")
                if existing_notes:
                    consolidated["notes"] = f"{existing_notes}\n\n{data['notes']}"
                else:
                    consolidated["notes"] = data["notes"]

        return consolidated

    def _parse_validation_result(
        self, result: Dict[str, Any], original_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Parse validation result from agent."""
        try:
            output = result.get("output", "")

            # Try to extract JSON validation result
            json_result = self._extract_json_from_text(output)

            if json_result and "validation_passed" in json_result:
                return json_result
            else:
                # Create validation result from text analysis
                return {
                    "validation_passed": "error" not in output.lower()
                    and "invalid" not in output.lower(),
                    "issues": self._extract_issues_from_text(output),
                    "corrected_data": original_data,
                    "validation_notes": output,
                }

        except Exception as e:
            logger.error(f"Error parsing validation result: {e}")
            return {
                "validation_passed": False,
                "error": str(e),
                "issues": [f"Validation parsing error: {e!s}"],
                "corrected_data": original_data,
            }

    def _extract_issues_from_text(self, text: str) -> List[str]:
        """Extract validation issues from text."""
        issues = []
        lines = text.split("\n")

        for line in lines:
            line = line.strip()
            if any(
                keyword in line.lower()
                for keyword in ["error", "missing", "invalid", "incorrect", "issue"]
            ):
                issues.append(line)

        return issues if issues else ["No specific issues identified"]
